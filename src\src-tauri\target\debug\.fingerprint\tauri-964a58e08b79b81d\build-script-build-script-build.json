{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 1925602030230079399, "deps": [[283161442353679854, "tauri_build", false, 16195717467808139106], [11050281405049894993, "tauri_utils", false, 11105958686551945476], [13077543566650298139, "heck", false, 10912606884005172386], [17155886227862585100, "glob", false, 17739493393613972577]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-964a58e08b79b81d\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}