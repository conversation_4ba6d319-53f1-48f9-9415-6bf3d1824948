{"rustc": 16591470773350601817, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 15174643492247994360, "deps": [[40386456601120721, "percent_encoding", false, 11774129644120267593], [442785307232013896, "tauri_runtime", false, 10827098626444979294], [1200537532907108615, "url<PERSON><PERSON>n", false, 7300258011113581726], [3150220818285335163, "url", false, 12155727394207725140], [4143744114649553716, "raw_window_handle", false, 2771300590775076479], [4341921533227644514, "muda", false, 5601705645580643381], [4919829919303820331, "serialize_to_javascript", false, 9165729944035803698], [5986029879202738730, "log", false, 14896891622662359858], [7752760652095876438, "tauri_runtime_wry", false, 16145732807850784107], [8539587424388551196, "webview2_com", false, 14483231888305795184], [9010263965687315507, "http", false, 16714832165723395293], [9228235415475680086, "tauri_macros", false, 1933752474766983657], [9538054652646069845, "tokio", false, 16945361508716146731], [9689903380558560274, "serde", false, 17399218494941812765], [9920160576179037441, "getrandom", false, 8314332398027686108], [10229185211513642314, "mime", false, 16676086155055312703], [10629569228670356391, "futures_util", false, 15232469423436694276], [10755362358622467486, "build_script_build", false, 8859229932691171144], [10806645703491011684, "thiserror", false, 11478485744550063485], [11050281405049894993, "tauri_utils", false, 16442616657148809703], [11989259058781683633, "dunce", false, 9940206787301489372], [12565293087094287914, "window_vibrancy", false, 9955063214495393206], [12986574360607194341, "serde_repr", false, 8237091298145490377], [13077543566650298139, "heck", false, 10912606884005172386], [13116089016666501665, "windows", false, 111976557147647525], [13625485746686963219, "anyhow", false, 2778989786511573041], [15367738274754116744, "serde_json", false, 5842069726811623790], [16928111194414003569, "dirs", false, 2712170411023066769], [17155886227862585100, "glob", false, 17739493393613972577]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-a092e60a279d248e\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}