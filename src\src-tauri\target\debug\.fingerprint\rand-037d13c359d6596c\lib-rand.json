{"rustc": 16591470773350601817, "features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"rand_pcg\", \"small_rng\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 8827111241893198906, "profile": 2225463790103693989, "path": 13948267875402314446, "deps": [[1333041802001714747, "rand_chacha", false, 9585589042215635344], [1740877332521282793, "rand_core", false, 17989686167966360143], [5170503507811329045, "getrandom_package", false, 4012338743850997437], [9875507072765444643, "rand_pcg", false, 16286563793831170816]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-037d13c359d6596c\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}