{"rustc": 16591470773350601817, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 11609390061373275792, "deps": [[3060637413840920116, "proc_macro2", false, 4493515871214349569], [7341521034400937459, "tauri_codegen", false, 13000684139035425032], [11050281405049894993, "tauri_utils", false, 11105958686551945476], [13077543566650298139, "heck", false, 10912606884005172386], [17990358020177143287, "quote", false, 9962594719828948336], [18149961000318489080, "syn", false, 1298049280683875459]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-a87def612a78179c\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}