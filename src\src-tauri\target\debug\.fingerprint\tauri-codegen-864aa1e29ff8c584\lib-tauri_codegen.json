{"rustc": 16591470773350601817, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 14135072835951534956, "deps": [[3060637413840920116, "proc_macro2", false, 4493515871214349569], [3150220818285335163, "url", false, 10105437426104814919], [4899080583175475170, "semver", false, 10650526094954674826], [7170110829644101142, "json_patch", false, 3596743938296522625], [7392050791754369441, "ico", false, 16635723430406163170], [8319709847752024821, "uuid", false, 6864783175008791648], [9689903380558560274, "serde", false, 2809665382707334249], [9857275760291862238, "sha2", false, 3061488309116161420], [10806645703491011684, "thiserror", false, 11478485744550063485], [11050281405049894993, "tauri_utils", false, 11105958686551945476], [12687914511023397207, "png", false, 1438467020417176857], [13077212702700853852, "base64", false, 18325299273121210509], [14132538657330703225, "brotli", false, 17868646577405114960], [15367738274754116744, "serde_json", false, 13179965326551841418], [15622660310229662834, "walkdir", false, 11350074574767057742], [17990358020177143287, "quote", false, 9962594719828948336], [18149961000318489080, "syn", false, 1298049280683875459]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-864aa1e29ff8c584\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}