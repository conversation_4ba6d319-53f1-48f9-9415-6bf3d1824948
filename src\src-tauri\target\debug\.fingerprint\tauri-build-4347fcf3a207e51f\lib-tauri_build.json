{"rustc": 16591470773350601817, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 15095947315323172637, "deps": [[4899080583175475170, "semver", false, 10650526094954674826], [6913375703034175521, "schemars", false, 9380620772262425792], [7170110829644101142, "json_patch", false, 3596743938296522625], [8786711029710048183, "toml", false, 5323147175062854116], [9689903380558560274, "serde", false, 2809665382707334249], [11050281405049894993, "tauri_utils", false, 11105958686551945476], [12714016054753183456, "tauri_winres", false, 3375787593728669172], [13077543566650298139, "heck", false, 10912606884005172386], [13475171727366188400, "cargo_toml", false, 16647414946354331759], [13625485746686963219, "anyhow", false, 2778989786511573041], [15367738274754116744, "serde_json", false, 13179965326551841418], [15622660310229662834, "walkdir", false, 11350074574767057742], [16928111194414003569, "dirs", false, 3271781850563233965], [17155886227862585100, "glob", false, 17739493393613972577]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-4347fcf3a207e51f\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}